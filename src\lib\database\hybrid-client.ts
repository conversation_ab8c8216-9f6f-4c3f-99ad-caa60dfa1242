/**
 * Hybrid Database Client
 * Combines MySQL (main data) with Supabase (real-time features)
 */

import { createClient } from '@supabase/supabase-js';

// Supabase client for real-time features
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }
);

// Hybrid Database Operations (Client-side only - no MySQL operations)
export class HybridDB {

  /**
   * Get user's real-time channel name
   */
  static getUserChannel(userId: string): string {
    return `user-${userId}`;
  }

  /**
   * Get conversation's real-time channel name
   */
  static getConversationChannel(conversationId: string): string {
    return `conversation-${conversationId}`;
  }

  /**
   * Subscribe to user's real-time events
   */
  static subscribeToUserEvents(
    userId: string,
    onMessage: (payload: any) => void,
    onNotification: (payload: any) => void
  ) {
    const channel = supabase
      .channel(this.getUserChannel(userId))
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_events',
          filter: `recipient_id=eq.${userId}`,
        },
        (payload) => {
          const event = payload.new as any;
          
          if (event.type === 'new_message') {
            onMessage(event);
          } else if (event.type === 'notification') {
            onNotification(event);
          }
        }
      )
      .subscribe();

    return channel;
  }

  /**
   * Unsubscribe from real-time events
   */
  static unsubscribe(channel: any) {
    return supabase.removeChannel(channel);
  }
}


